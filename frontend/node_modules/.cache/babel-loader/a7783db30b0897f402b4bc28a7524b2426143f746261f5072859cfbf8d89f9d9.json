{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultView = ({\n  result,\n  originalImage\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [zoomLevel, setZoomLevel] = useState(1); // 缩放级别\n  const [panOffset, setPanOffset] = useState({\n    x: 0,\n    y: 0\n  }); // 平移偏移\n  const [isPanning, setIsPanning] = useState(false);\n  const [lastPanPoint, setLastPanPoint] = useState({\n    x: 0,\n    y: 0\n  });\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const imageContainerRef = useRef(null);\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleSplitMouseDown = e => {\n    setIsDragging(true);\n    e.preventDefault();\n    e.stopPropagation(); // 阻止事件冒泡，避免触发图像平移\n  };\n  const handleSplitMouseMove = e => {\n    if (!isDragging || !imageContainerRef.current) return;\n    const rect = imageContainerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, x / rect.width * 100));\n    setSplitPosition(percentage);\n  };\n  const handleSplitMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 缩放处理\n  const handleWheel = e => {\n    if (!imageContainerRef.current) return;\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? -0.1 : 0.1;\n    const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));\n    setZoomLevel(newZoom);\n  };\n\n  // 平移处理\n  const handlePanStart = e => {\n    if (zoomLevel <= 1 || isDragging) return; // 只有放大时才允许平移，且不在拖拽分割线时\n\n    setIsPanning(true);\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n    e.preventDefault();\n  };\n\n  // 图像容器的鼠标按下处理（避免与分割线拖拽冲突）\n  const handleImageMouseDown = e => {\n    // 检查是否点击在分割线上（通过检查目标元素）\n    if (e.target.closest('[data-split-line]')) {\n      return; // 如果点击在分割线上，不处理平移\n    }\n    if (zoomLevel > 1 && !isDragging) {\n      handlePanStart(e);\n    }\n  };\n  const handlePanMove = e => {\n    if (!isPanning) return;\n    const deltaX = e.clientX - lastPanPoint.x;\n    const deltaY = e.clientY - lastPanPoint.y;\n    setPanOffset(prev => ({\n      x: prev.x + deltaX,\n      y: prev.y + deltaY\n    }));\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n  };\n  const handlePanEnd = () => {\n    setIsPanning(false);\n  };\n\n  // 重置缩放和平移\n  const resetZoom = () => {\n    setZoomLevel(1);\n    setPanOffset({\n      x: 0,\n      y: 0\n    });\n  };\n\n  // 缩放控制函数\n  const zoomIn = () => {\n    setZoomLevel(prev => Math.min(5, prev + 0.25));\n  };\n  const zoomOut = () => {\n    setZoomLevel(prev => Math.max(0.5, prev - 0.25));\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleSplitMouseMove);\n      document.addEventListener('mouseup', handleSplitMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleSplitMouseMove);\n        document.removeEventListener('mouseup', handleSplitMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 添加平移事件监听\n  useEffect(() => {\n    if (isPanning) {\n      document.addEventListener('mousemove', handlePanMove);\n      document.addEventListener('mouseup', handlePanEnd);\n      return () => {\n        document.removeEventListener('mousemove', handlePanMove);\n        document.removeEventListener('mouseup', handlePanEnd);\n      };\n    }\n  }, [isPanning, lastPanPoint]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '13px',\n            color: '#ddd'\n          },\n          children: \"\\u5904\\u7406\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), result.message && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [\"\\u2022 \", result.message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '6px',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('split'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5206\\u5272\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('side-by-side'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), viewMode === 'split' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetSplit,\n          style: {\n            padding: '4px 8px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), result.params_used && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: showParams ? 'auto' : '32px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        padding: '8px 16px',\n        flexShrink: 0,\n        transition: 'height 0.3s ease'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u5904\\u7406\\u53C2\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), !showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              fontSize: '12px',\n              color: '#ddd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [result.params_used.scale, \"x\\u8D85\\u5206\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u9510\\u5316\", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u7F8E\\u989C\", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowParams(!showParams),\n          style: {\n            padding: '2px 6px',\n            backgroundColor: 'transparent',\n            color: '#aaa',\n            border: '1px solid #555',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px',\n            transition: 'all 0.2s'\n          },\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = '#555';\n            e.target.style.color = '#fff';\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n            e.target.style.color = '#aaa';\n          },\n          children: showParams ? '▲' : '▼'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this), showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '12px',\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',\n          gap: '8px',\n          fontSize: '12px',\n          color: '#ddd'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u8D85\\u5206\\u500D\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 20\n          }, this), \" \", result.params_used.scale, \"x\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"AI\\u6A21\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 20\n          }, this), \" \", result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9510\\u5316:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u964D\\u566A:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 20\n          }, this), \" \", result.params_used.denoising]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9971\\u548C\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 20\n          }, this), \" \", result.params_used.saturation.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u5BF9\\u6BD4\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 20\n          }, this), \" \", result.params_used.contrast.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u4EAE\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 20\n          }, this), \" \", result.params_used.brightness > 0 ? '+' : '', result.params_used.brightness]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u7F8E\\u989C:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      },\n      children: viewMode === 'split' ?\n      /*#__PURE__*/\n      // 分割线对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u59CB\\u56FE\\u50CF | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A\\u56FE\\u50CF | \\u5206\\u5272\\u4F4D\\u7F6E\\uFF1A\", splitPosition.toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              position: 'relative',\n              zIndex: 1000 // 确保按钮始终在最上层\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomOut,\n              disabled: zoomLevel <= 0.5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u7F29\\u5C0F\",\n              children: \"\\u2212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                minWidth: '40px',\n                textAlign: 'center',\n                fontSize: '11px',\n                color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n              },\n              children: [(zoomLevel * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomIn,\n              disabled: zoomLevel >= 5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                color: zoomLevel >= 5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u653E\\u5927\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetZoom,\n              disabled: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#444' : '#555',\n                color: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? 'not-allowed' : 'pointer',\n                fontSize: '10px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u91CD\\u7F6E\\u7F29\\u653E\",\n              children: \"\\u2302\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: containerRef,\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            cursor: isDragging ? 'ew-resize' : 'default',\n            userSelect: 'none',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            overflow: 'auto' // 添加滚动条支持\n          },\n          children: originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              maxHeight: 'calc(100vh - 200px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: imageContainerRef,\n              style: {\n                position: 'relative',\n                display: 'inline-block',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                overflow: 'hidden',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: handleImageMouseDown,\n              children: [imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '400px',\n                  height: '300px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                },\n                children: \"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                ref: enhancedImageRef,\n                src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n                alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 250px)',\n                  height: 'auto',\n                  display: 'block',\n                  opacity: imageLoaded ? 1 : 0.5,\n                  imageRendering: 'auto' // 增强图像使用高质量渲染\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 23\n              }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: `${splitPosition}%`,\n                  height: '100%',\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: originalImage,\n                  alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n                  style: {\n                    // 确保原图与增强图片尺寸完全匹配\n                    width: `${100 * 100 / splitPosition}%`,\n                    height: '100%',\n                    objectFit: 'fill',\n                    // 强制填充，实现最近邻插值效果\n                    imageRendering: 'pixelated',\n                    // 最近邻插值，保持像素清晰\n                    position: 'absolute',\n                    left: 0,\n                    top: 0\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 23\n              }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                \"data-split-line\": \"true\",\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: `${splitPosition}%`,\n                  width: '2px',\n                  height: '100%',\n                  backgroundColor: '#4a90e2',\n                  cursor: 'ew-resize',\n                  boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                  transform: 'translateX(-1px)',\n                  zIndex: 10\n                },\n                onMouseDown: handleSplitMouseDown,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    width: '16px',\n                    height: '32px',\n                    backgroundColor: '#4a90e2',\n                    borderRadius: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '10px',\n                    fontWeight: 'bold',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                  },\n                  children: \"\\u27F7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 23\n              }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  color: '#aaa',\n                  backgroundColor: 'rgba(43,43,43,0.9)',\n                  padding: '8px 12px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: \"\\u52A0\\u8F7D\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // 并排对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\\u6A21\\u5F0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              position: 'relative',\n              zIndex: 1000 // 确保按钮始终在最上层\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomOut,\n              disabled: zoomLevel <= 0.5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u7F29\\u5C0F\",\n              children: \"\\u2212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                minWidth: '40px',\n                textAlign: 'center',\n                fontSize: '11px',\n                color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n              },\n              children: [(zoomLevel * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomIn,\n              disabled: zoomLevel >= 5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                color: zoomLevel >= 5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u653E\\u5927\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetZoom,\n              disabled: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#444' : '#555',\n                color: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? 'not-allowed' : 'pointer',\n                fontSize: '10px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u91CD\\u7F6E\\u7F29\\u653E\",\n              children: \"\\u2302\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            gap: '16px',\n            alignItems: 'center',\n            justifyContent: 'center',\n            overflow: 'auto' // 添加滚动条支持\n          },\n          children: [originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              textAlign: 'center',\n              maxWidth: '50%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                fontSize: '12px',\n                color: '#aaa'\n              },\n              children: \"\\u539F\\u59CB\\u56FE\\u50CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'inline-block',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: handleImageMouseDown,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: originalImage,\n                alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 300px)',\n                  height: 'auto',\n                  border: '1px solid #4a90e2',\n                  borderRadius: '4px',\n                  objectFit: 'contain',\n                  imageRendering: 'pixelated' // 最近邻插值，保持像素清晰\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              textAlign: 'center',\n              maxWidth: '50%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                fontSize: '12px',\n                color: '#aaa'\n              },\n              children: \"\\u589E\\u5F3A\\u56FE\\u50CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 15\n            }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '200px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '1px dashed #666',\n                borderRadius: '4px',\n                color: '#aaa',\n                backgroundColor: '#2b2b2b'\n              },\n              children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 17\n            }, this), imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '200px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '1px solid #ff6b6b',\n                borderRadius: '4px',\n                color: '#ff6b6b',\n                backgroundColor: '#2b2b2b'\n              },\n              children: \"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: imageLoaded ? 'inline-block' : 'none',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: handleImageMouseDown,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n                alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 300px)',\n                  height: 'auto',\n                  border: '1px solid #28ca42',\n                  borderRadius: '4px',\n                  objectFit: 'contain'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '48px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        gap: '12px',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadImage,\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#28ca42',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#22a83a',\n        onMouseLeave: e => e.target.style.backgroundColor = '#28ca42',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCE5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 13\n        }, this), \"\\u4E0B\\u8F7D\\u56FE\\u50CF\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 846,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `http://localhost:8001/result/${result.enhanced_url}`,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#4a90e2',\n          color: 'white',\n          textDecoration: 'none',\n          borderRadius: '4px',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#357abd',\n        onMouseLeave: e => e.target.style.backgroundColor = '#4a90e2',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 13\n        }, this), \"\\u65B0\\u7A97\\u53E3\\u67E5\\u770B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#aaa',\n          marginLeft: 'auto'\n        },\n        children: [\"\\u6587\\u4EF6: \", result.filename]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 891,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 835,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultView, \"aHMVuXC9EAhFri4aNmdgG0SWmPI=\");\n_c = ResultView;\nexport default ResultView;\nvar _c;\n$RefreshReg$(_c, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "ResultView", "result", "originalImage", "_s", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "showParams", "setShowParams", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "zoomLevel", "setZoomLevel", "panOffset", "setPanOffset", "x", "y", "isPanning", "setIsPanning", "lastPanPoint", "setLastPanPoint", "containerRef", "enhancedImageRef", "imageContainerRef", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleSplitMouseDown", "e", "preventDefault", "stopPropagation", "handleSplitMouseMove", "current", "rect", "getBoundingClientRect", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleSplitMouseUp", "handleWheel", "delta", "deltaY", "newZoom", "handlePanStart", "clientY", "handleImageMouseDown", "target", "closest", "handlePanMove", "deltaX", "prev", "handlePanEnd", "resetZoom", "zoomIn", "zoomOut", "addEventListener", "removeEventListener", "resetSplit", "style", "height", "display", "flexDirection", "backgroundColor", "color", "children", "borderBottom", "alignItems", "justifyContent", "padding", "flexShrink", "gap", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "message", "onClick", "border", "cursor", "transition", "params_used", "scale", "use_realesrgan", "sharpening", "toFixed", "beauty", "onMouseEnter", "onMouseLeave", "marginTop", "gridTemplateColumns", "denoising", "saturation", "contrast", "brightness", "flex", "overflow", "position", "marginBottom", "zIndex", "disabled", "title", "min<PERSON><PERSON><PERSON>", "textAlign", "ref", "userSelect", "maxHeight", "transform", "transform<PERSON><PERSON>in", "onWheel", "onMouseDown", "src", "Date", "now", "alt", "onLoad", "onError", "max<PERSON><PERSON><PERSON>", "opacity", "imageRendering", "top", "objectFit", "boxShadow", "fontWeight", "borderTop", "rel", "textDecoration", "marginLeft", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [zoomLevel, setZoomLevel] = useState(1); // 缩放级别\n  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 }); // 平移偏移\n  const [isPanning, setIsPanning] = useState(false);\n  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const imageContainerRef = useRef(null);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `http://localhost:8001/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleSplitMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n    e.stopPropagation(); // 阻止事件冒泡，避免触发图像平移\n  };\n\n  const handleSplitMouseMove = (e) => {\n    if (!isDragging || !imageContainerRef.current) return;\n\n    const rect = imageContainerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleSplitMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 缩放处理\n  const handleWheel = (e) => {\n    if (!imageContainerRef.current) return;\n\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? -0.1 : 0.1;\n    const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));\n    setZoomLevel(newZoom);\n  };\n\n  // 平移处理\n  const handlePanStart = (e) => {\n    if (zoomLevel <= 1 || isDragging) return; // 只有放大时才允许平移，且不在拖拽分割线时\n\n    setIsPanning(true);\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n    e.preventDefault();\n  };\n\n  // 图像容器的鼠标按下处理（避免与分割线拖拽冲突）\n  const handleImageMouseDown = (e) => {\n    // 检查是否点击在分割线上（通过检查目标元素）\n    if (e.target.closest('[data-split-line]')) {\n      return; // 如果点击在分割线上，不处理平移\n    }\n\n    if (zoomLevel > 1 && !isDragging) {\n      handlePanStart(e);\n    }\n  };\n\n  const handlePanMove = (e) => {\n    if (!isPanning) return;\n\n    const deltaX = e.clientX - lastPanPoint.x;\n    const deltaY = e.clientY - lastPanPoint.y;\n\n    setPanOffset(prev => ({\n      x: prev.x + deltaX,\n      y: prev.y + deltaY\n    }));\n\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n  };\n\n  const handlePanEnd = () => {\n    setIsPanning(false);\n  };\n\n  // 重置缩放和平移\n  const resetZoom = () => {\n    setZoomLevel(1);\n    setPanOffset({ x: 0, y: 0 });\n  };\n\n  // 缩放控制函数\n  const zoomIn = () => {\n    setZoomLevel(prev => Math.min(5, prev + 0.25));\n  };\n\n  const zoomOut = () => {\n    setZoomLevel(prev => Math.max(0.5, prev - 0.25));\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleSplitMouseMove);\n      document.addEventListener('mouseup', handleSplitMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleSplitMouseMove);\n        document.removeEventListener('mouseup', handleSplitMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 添加平移事件监听\n  useEffect(() => {\n    if (isPanning) {\n      document.addEventListener('mousemove', handlePanMove);\n      document.addEventListener('mouseup', handlePanEnd);\n      return () => {\n        document.removeEventListener('mousemove', handlePanMove);\n        document.removeEventListener('mouseup', handlePanEnd);\n      };\n    }\n  }, [isPanning, lastPanPoint]);\n\n\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ \n      width: '100%', \n      height: '100%', \n      display: 'flex', \n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    }}>\n      {/* 顶部工具栏 */}\n      <div style={{\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      }}>\n        {/* 左侧状态指示 */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }}></div>\n          <span style={{ fontSize: '13px', color: '#ddd' }}>处理完成</span>\n          {result.message && (\n            <span style={{ fontSize: '12px', color: '#aaa' }}>• {result.message}</span>\n          )}\n        </div>\n\n        {/* 右侧视图控制 */}\n        <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px',\n                transition: 'background-color 0.2s'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 参数信息栏 */}\n      {result.params_used && (\n        <div style={{\n          height: showParams ? 'auto' : '32px',\n          backgroundColor: '#2b2b2b',\n          borderBottom: '1px solid #555',\n          padding: '8px 16px',\n          flexShrink: 0,\n          transition: 'height 0.3s ease'\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n              <span style={{ fontSize: '12px', color: '#aaa' }}>处理参数:</span>\n              {!showParams && (\n                <div style={{ display: 'flex', gap: '12px', fontSize: '12px', color: '#ddd' }}>\n                  <span>{result.params_used.scale}x超分</span>\n                  <span>{result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'}</span>\n                  <span>锐化{(result.params_used.sharpening * 100).toFixed(0)}%</span>\n                  <span>美颜{(result.params_used.beauty * 100).toFixed(0)}%</span>\n                </div>\n              )}\n            </div>\n            <button\n              onClick={() => setShowParams(!showParams)}\n              style={{\n                padding: '2px 6px',\n                backgroundColor: 'transparent',\n                color: '#aaa',\n                border: '1px solid #555',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px',\n                transition: 'all 0.2s'\n              }}\n              onMouseEnter={(e) => {\n                e.target.style.backgroundColor = '#555';\n                e.target.style.color = '#fff';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.backgroundColor = 'transparent';\n                e.target.style.color = '#aaa';\n              }}\n            >\n              {showParams ? '▲' : '▼'}\n            </button>\n          </div>\n\n          {showParams && (\n            <div style={{ \n              marginTop: '12px', \n              display: 'grid', \n              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', \n              gap: '8px',\n              fontSize: '12px',\n              color: '#ddd'\n            }}>\n              <div><span style={{ color: '#aaa' }}>超分倍数:</span> {result.params_used.scale}x</div>\n              <div><span style={{ color: '#aaa' }}>AI模型:</span> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n              <div><span style={{ color: '#aaa' }}>锐化:</span> {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n              <div><span style={{ color: '#aaa' }}>降噪:</span> {result.params_used.denoising}</div>\n              <div><span style={{ color: '#aaa' }}>饱和度:</span> {result.params_used.saturation.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>对比度:</span> {result.params_used.contrast.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>亮度:</span> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n              <div><span style={{ color: '#aaa' }}>美颜:</span> {(result.params_used.beauty * 100).toFixed(0)}%</div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 主图像显示区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex', \n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      }}>\n        {viewMode === 'split' ? (\n          // 分割线对比模式\n          <div style={{ \n            flex: 1, \n            display: 'flex', \n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 分割线信息栏和缩放控制 */}\n            <div style={{\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                <span>左侧：原始图像 | 右侧：增强图像 | 分割位置：{splitPosition.toFixed(0)}%</span>\n              </div>\n\n              {/* 缩放控制按钮 */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                position: 'relative',\n                zIndex: 1000 // 确保按钮始终在最上层\n              }}>\n                <button\n                  onClick={zoomOut}\n                  disabled={zoomLevel <= 0.5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                    color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"缩小\"\n                >\n                  −\n                </button>\n\n                <span style={{\n                  minWidth: '40px',\n                  textAlign: 'center',\n                  fontSize: '11px',\n                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n                }}>\n                  {(zoomLevel * 100).toFixed(0)}%\n                </span>\n\n                <button\n                  onClick={zoomIn}\n                  disabled={zoomLevel >= 5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                    color: zoomLevel >= 5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"放大\"\n                >\n                  +\n                </button>\n\n                <button\n                  onClick={resetZoom}\n                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',\n                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',\n                    fontSize: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"重置缩放\"\n                >\n                  ⌂\n                </button>\n              </div>\n            </div>\n\n            <div\n              ref={containerRef}\n              style={{\n                position: 'relative',\n                width: '100%',\n                height: '100%',\n                cursor: isDragging ? 'ew-resize' : 'default',\n                userSelect: 'none',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                overflow: 'auto' // 添加滚动条支持\n              }}\n            >\n              {originalImage && (\n                <div style={{ \n                  position: 'relative', \n                  width: '100%', \n                  height: '100%',\n                  maxHeight: 'calc(100vh - 200px)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  {/* 图像对比容器 - 支持缩放和平移 */}\n                  <div\n                    ref={imageContainerRef}\n                    style={{\n                      position: 'relative',\n                      display: 'inline-block',\n                      border: '1px solid #555',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                      transformOrigin: 'center center',\n                      transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                      cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                    }}\n                    onWheel={handleWheel}\n                    onMouseDown={handleImageMouseDown}\n                  >\n                    {/* 增强图像作为背景 */}\n                    {imageError ? (\n                      <div style={{\n                        width: '400px',\n                        height: '300px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        color: '#ff6b6b',\n                        backgroundColor: '#2b2b2b'\n                      }}>\n                        增强图像加载失败\n                      </div>\n                    ) : (\n                      <img\n                        ref={enhancedImageRef}\n                        src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                        alt=\"增强图像\"\n                        onLoad={handleImageLoad}\n                        onError={handleImageError}\n                        style={{\n                          maxWidth: '100%',\n                          maxHeight: 'calc(100vh - 250px)',\n                          height: 'auto',\n                          display: 'block',\n                          opacity: imageLoaded ? 1 : 0.5,\n                          imageRendering: 'auto' // 增强图像使用高质量渲染\n                        }}\n                      />\n                    )}\n\n                    {/* 原始图像覆盖层 - 像素级对齐 */}\n                    {imageLoaded && (\n                      <div\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          width: `${splitPosition}%`,\n                          height: '100%',\n                          overflow: 'hidden'\n                        }}\n                      >\n                        <img\n                          src={originalImage}\n                          alt=\"原始图像\"\n                          style={{\n                            // 确保原图与增强图片尺寸完全匹配\n                            width: `${100 * 100 / splitPosition}%`,\n                            height: '100%',\n                            objectFit: 'fill', // 强制填充，实现最近邻插值效果\n                            imageRendering: 'pixelated', // 最近邻插值，保持像素清晰\n                            position: 'absolute',\n                            left: 0,\n                            top: 0\n                          }}\n                        />\n                      </div>\n                    )}\n\n                    {/* 分割线 */}\n                    {imageLoaded && (\n                      <div\n                        data-split-line=\"true\"\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: `${splitPosition}%`,\n                          width: '2px',\n                          height: '100%',\n                          backgroundColor: '#4a90e2',\n                          cursor: 'ew-resize',\n                          boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                          transform: 'translateX(-1px)',\n                          zIndex: 10\n                        }}\n                        onMouseDown={handleSplitMouseDown}\n                      >\n                        {/* 分割线手柄 */}\n                        <div\n                          style={{\n                            position: 'absolute',\n                            top: '50%',\n                            left: '50%',\n                            transform: 'translate(-50%, -50%)',\n                            width: '16px',\n                            height: '32px',\n                            backgroundColor: '#4a90e2',\n                            borderRadius: '8px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontSize: '10px',\n                            fontWeight: 'bold',\n                            boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                          }}\n                        >\n                          ⟷\n                        </div>\n                      </div>\n                    )}\n\n                    {/* 加载状态 */}\n                    {!imageLoaded && !imageError && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        color: '#aaa',\n                        backgroundColor: 'rgba(43,43,43,0.9)',\n                        padding: '8px 12px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      }}>\n                        加载中...\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        ) : (\n          // 并排对比模式\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 并排对比控制栏 */}\n            <div style={{\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <span>并排对比模式</span>\n\n              {/* 缩放控制按钮 */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                position: 'relative',\n                zIndex: 1000 // 确保按钮始终在最上层\n              }}>\n                <button\n                  onClick={zoomOut}\n                  disabled={zoomLevel <= 0.5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                    color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"缩小\"\n                >\n                  −\n                </button>\n\n                <span style={{\n                  minWidth: '40px',\n                  textAlign: 'center',\n                  fontSize: '11px',\n                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n                }}>\n                  {(zoomLevel * 100).toFixed(0)}%\n                </span>\n\n                <button\n                  onClick={zoomIn}\n                  disabled={zoomLevel >= 5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                    color: zoomLevel >= 5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"放大\"\n                >\n                  +\n                </button>\n\n                <button\n                  onClick={resetZoom}\n                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',\n                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',\n                    fontSize: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"重置缩放\"\n                >\n                  ⌂\n                </button>\n              </div>\n            </div>\n\n            {/* 并排图像容器 */}\n            <div style={{\n              flex: 1,\n              display: 'flex',\n              gap: '16px',\n              alignItems: 'center',\n              justifyContent: 'center',\n              overflow: 'auto' // 添加滚动条支持\n            }}>\n            {/* 原始图像 */}\n            {originalImage && (\n              <div style={{ \n                flex: '1', \n                textAlign: 'center',\n                maxWidth: '50%'\n              }}>\n                <div style={{ \n                  marginBottom: '8px', \n                  fontSize: '12px', \n                  color: '#aaa' \n                }}>\n                  原始图像\n                </div>\n                <div\n                  style={{\n                    display: 'inline-block',\n                    transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                    transformOrigin: 'center center',\n                    transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                    cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                  }}\n                  onWheel={handleWheel}\n                  onMouseDown={handleImageMouseDown}\n                >\n                  <img\n                    src={originalImage}\n                    alt=\"原始图像\"\n                    style={{\n                      maxWidth: '100%',\n                      maxHeight: 'calc(100vh - 300px)',\n                      height: 'auto',\n                      border: '1px solid #4a90e2',\n                      borderRadius: '4px',\n                      objectFit: 'contain',\n                      imageRendering: 'pixelated' // 最近邻插值，保持像素清晰\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* 增强图像 */}\n            <div style={{ \n              flex: '1', \n              textAlign: 'center',\n              maxWidth: '50%'\n            }}>\n              <div style={{ \n                marginBottom: '8px', \n                fontSize: '12px', \n                color: '#aaa' \n              }}>\n                增强图像\n              </div>\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px dashed #666',\n                  borderRadius: '4px',\n                  color: '#aaa',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  正在加载图像...\n                </div>\n              )}\n\n              {imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px solid #ff6b6b',\n                  borderRadius: '4px',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  图像加载失败\n                </div>\n              )}\n\n              <div\n                style={{\n                  display: imageLoaded ? 'inline-block' : 'none',\n                  transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                  transformOrigin: 'center center',\n                  transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                  cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                }}\n                onWheel={handleWheel}\n                onMouseDown={handleImageMouseDown}\n              >\n                <img\n                  src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                  alt=\"增强图像\"\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: 'calc(100vh - 300px)',\n                    height: 'auto',\n                    border: '1px solid #28ca42',\n                    borderRadius: '4px',\n                    objectFit: 'contain'\n                  }}\n                />\n              </div>\n            </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 底部控制栏 */}\n      {imageLoaded && (\n        <div style={{\n          height: '48px',\n          backgroundColor: '#333',\n          borderTop: '1px solid #555',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '12px',\n          padding: '0 16px',\n          flexShrink: 0\n        }}>\n          <button\n            onClick={downloadImage}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#28ca42',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#22a83a'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#28ca42'}\n          >\n            <span>📥</span>\n            下载图像\n          </button>\n\n          <a\n            href={`http://localhost:8001/result/${result.enhanced_url}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              textDecoration: 'none',\n              borderRadius: '4px',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#357abd'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#4a90e2'}\n          >\n            <span>🔍</span>\n            新窗口查看\n          </a>\n\n          <div style={{\n            fontSize: '12px',\n            color: '#aaa',\n            marginLeft: 'auto'\n          }}>\n            文件: {result.filename}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC;IAAEyB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC;IAAEyB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChE,MAAMK,YAAY,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM+B,gBAAgB,GAAG/B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMgC,iBAAiB,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAEtC,MAAMiC,eAAe,GAAGA,CAAA,KAAM;IAC5BxB,cAAc,CAAC,IAAI,CAAC;IACpBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvB,aAAa,CAAC,IAAI,CAAC;IACnBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,gCAAgClC,MAAM,CAACmC,YAAY,EAAE;IACjEJ,IAAI,CAACK,QAAQ,GAAG,YAAYpC,MAAM,CAACqC,QAAQ,EAAE;IAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;IACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMW,oBAAoB,GAAIC,CAAC,IAAK;IAClC/B,aAAa,CAAC,IAAI,CAAC;IACnB+B,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAIH,CAAC,IAAK;IAClC,IAAI,CAAChC,UAAU,IAAI,CAACgB,iBAAiB,CAACoB,OAAO,EAAE;IAE/C,MAAMC,IAAI,GAAGrB,iBAAiB,CAACoB,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAC9D,MAAM9B,CAAC,GAAGwB,CAAC,CAACO,OAAO,GAAGF,IAAI,CAACG,IAAI;IAC/B,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAGpC,CAAC,GAAG6B,IAAI,CAACQ,KAAK,GAAI,GAAG,CAAC,CAAC;IACrE9C,gBAAgB,CAAC0C,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/B7C,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAM8C,WAAW,GAAIf,CAAC,IAAK;IACzB,IAAI,CAAChB,iBAAiB,CAACoB,OAAO,EAAE;IAEhCJ,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMe,KAAK,GAAGhB,CAAC,CAACiB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG;IACvC,MAAMC,OAAO,GAAGR,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAExC,SAAS,GAAG4C,KAAK,CAAC,CAAC;IAC7D3C,YAAY,CAAC6C,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAInB,CAAC,IAAK;IAC5B,IAAI5B,SAAS,IAAI,CAAC,IAAIJ,UAAU,EAAE,OAAO,CAAC;;IAE1CW,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC;MACdL,CAAC,EAAEwB,CAAC,CAACO,OAAO;MACZ9B,CAAC,EAAEuB,CAAC,CAACoB;IACP,CAAC,CAAC;IACFpB,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMoB,oBAAoB,GAAIrB,CAAC,IAAK;IAClC;IACA,IAAIA,CAAC,CAACsB,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,EAAE;MACzC,OAAO,CAAC;IACV;IAEA,IAAInD,SAAS,GAAG,CAAC,IAAI,CAACJ,UAAU,EAAE;MAChCmD,cAAc,CAACnB,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,aAAa,GAAIxB,CAAC,IAAK;IAC3B,IAAI,CAACtB,SAAS,EAAE;IAEhB,MAAM+C,MAAM,GAAGzB,CAAC,CAACO,OAAO,GAAG3B,YAAY,CAACJ,CAAC;IACzC,MAAMyC,MAAM,GAAGjB,CAAC,CAACoB,OAAO,GAAGxC,YAAY,CAACH,CAAC;IAEzCF,YAAY,CAACmD,IAAI,KAAK;MACpBlD,CAAC,EAAEkD,IAAI,CAAClD,CAAC,GAAGiD,MAAM;MAClBhD,CAAC,EAAEiD,IAAI,CAACjD,CAAC,GAAGwC;IACd,CAAC,CAAC,CAAC;IAEHpC,eAAe,CAAC;MACdL,CAAC,EAAEwB,CAAC,CAACO,OAAO;MACZ9B,CAAC,EAAEuB,CAAC,CAACoB;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzBhD,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMiD,SAAS,GAAGA,CAAA,KAAM;IACtBvD,YAAY,CAAC,CAAC,CAAC;IACfE,YAAY,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMoD,MAAM,GAAGA,CAAA,KAAM;IACnBxD,YAAY,CAACqD,IAAI,IAAIhB,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEc,IAAI,GAAG,IAAI,CAAC,CAAC;EAChD,CAAC;EAED,MAAMI,OAAO,GAAGA,CAAA,KAAM;IACpBzD,YAAY,CAACqD,IAAI,IAAIhB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEe,IAAI,GAAG,IAAI,CAAC,CAAC;EAClD,CAAC;;EAED;EACAzE,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,EAAE;MACdqB,QAAQ,CAAC0C,gBAAgB,CAAC,WAAW,EAAE5B,oBAAoB,CAAC;MAC5Dd,QAAQ,CAAC0C,gBAAgB,CAAC,SAAS,EAAEjB,kBAAkB,CAAC;MACxD,OAAO,MAAM;QACXzB,QAAQ,CAAC2C,mBAAmB,CAAC,WAAW,EAAE7B,oBAAoB,CAAC;QAC/Dd,QAAQ,CAAC2C,mBAAmB,CAAC,SAAS,EAAElB,kBAAkB,CAAC;MAC7D,CAAC;IACH;EACF,CAAC,EAAE,CAAC9C,UAAU,CAAC,CAAC;;EAEhB;EACAf,SAAS,CAAC,MAAM;IACd,IAAIyB,SAAS,EAAE;MACbW,QAAQ,CAAC0C,gBAAgB,CAAC,WAAW,EAAEP,aAAa,CAAC;MACrDnC,QAAQ,CAAC0C,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClD,OAAO,MAAM;QACXtC,QAAQ,CAAC2C,mBAAmB,CAAC,WAAW,EAAER,aAAa,CAAC;QACxDnC,QAAQ,CAAC2C,mBAAmB,CAAC,SAAS,EAAEL,YAAY,CAAC;MACvD,CAAC;IACH;EACF,CAAC,EAAE,CAACjD,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAI7B;EACA,MAAMqD,UAAU,GAAGA,CAAA,KAAM;IACvBlE,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEZ,OAAA;IAAK+E,KAAK,EAAE;MACVrB,KAAK,EAAE,MAAM;MACbsB,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBAEArF,OAAA;MAAK+E,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BL,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBAEArF,OAAA;QAAK+E,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAO,CAAE;QAAAN,QAAA,gBACjErF,OAAA;UAAK+E,KAAK,EAAE;YACVrB,KAAK,EAAE,KAAK;YACZsB,MAAM,EAAE,KAAK;YACbY,YAAY,EAAE,KAAK;YACnBT,eAAe,EAAE;UACnB;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACThG,OAAA;UAAM+E,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EAAC;QAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC5D9F,MAAM,CAACgG,OAAO,iBACblG,OAAA;UAAM+E,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,GAAC,SAAE,EAACnF,MAAM,CAACgG,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC3E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhG,OAAA;QAAK+E,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,KAAK;UAAEJ,UAAU,EAAE;QAAS,CAAE;QAAAF,QAAA,gBAChErF,OAAA;UACEmG,OAAO,EAAEA,CAAA,KAAMnF,WAAW,CAAC,OAAO,CAAE;UACpC+D,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAEpE,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,MAAM;YAC1DqE,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThG,OAAA;UACEmG,OAAO,EAAEA,CAAA,KAAMnF,WAAW,CAAC,cAAc,CAAE;UAC3C+D,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAEpE,QAAQ,KAAK,cAAc,GAAG,SAAS,GAAG,MAAM;YACjEqE,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRjF,QAAQ,KAAK,OAAO,iBACnBf,OAAA;UACEmG,OAAO,EAAErB,UAAW;UACpBC,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9F,MAAM,CAACqG,WAAW,iBACjBvG,OAAA;MAAK+E,KAAK,EAAE;QACVC,MAAM,EAAEvE,UAAU,GAAG,MAAM,GAAG,MAAM;QACpC0E,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BG,OAAO,EAAE,UAAU;QACnBC,UAAU,EAAE,CAAC;QACbY,UAAU,EAAE;MACd,CAAE;MAAAjB,QAAA,gBACArF,OAAA;QAAK+E,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAH,QAAA,gBACrFrF,OAAA;UAAK+E,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE,QAAQ;YAAEI,GAAG,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACjErF,OAAA;YAAM+E,KAAK,EAAE;cAAEkB,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC7D,CAACvF,UAAU,iBACVT,OAAA;YAAK+E,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEU,GAAG,EAAE,MAAM;cAAEM,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBAC5ErF,OAAA;cAAAqF,QAAA,GAAOnF,MAAM,CAACqG,WAAW,CAACC,KAAK,EAAC,eAAG;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1ChG,OAAA;cAAAqF,QAAA,EAAOnF,MAAM,CAACqG,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtEhG,OAAA;cAAAqF,QAAA,GAAM,cAAE,EAAC,CAACnF,MAAM,CAACqG,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClEhG,OAAA;cAAAqF,QAAA,GAAM,cAAE,EAAC,CAACnF,MAAM,CAACqG,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNhG,OAAA;UACEmG,OAAO,EAAEA,CAAA,KAAMzF,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1CsE,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,aAAa;YAC9BC,KAAK,EAAE,MAAM;YACbgB,MAAM,EAAE,gBAAgB;YACxBR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UACFO,YAAY,EAAGhE,CAAC,IAAK;YACnBA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,MAAM;YACvCtC,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,CAAE;UACF0B,YAAY,EAAGjE,CAAC,IAAK;YACnBA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,aAAa;YAC9CtC,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,CAAE;UAAAC,QAAA,EAED5E,UAAU,GAAG,GAAG,GAAG;QAAG;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELvF,UAAU,iBACTT,OAAA;QAAK+E,KAAK,EAAE;UACVgC,SAAS,EAAE,MAAM;UACjB9B,OAAO,EAAE,MAAM;UACf+B,mBAAmB,EAAE,sCAAsC;UAC3DrB,GAAG,EAAE,KAAK;UACVM,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACArF,OAAA;UAAAqF,QAAA,gBAAKrF,OAAA;YAAM+E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC9F,MAAM,CAACqG,WAAW,CAACC,KAAK,EAAC,GAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnFhG,OAAA;UAAAqF,QAAA,gBAAKrF,OAAA;YAAM+E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC9F,MAAM,CAACqG,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG,MAAM;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnHhG,OAAA;UAAAqF,QAAA,gBAAKrF,OAAA;YAAM+E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAAC9F,MAAM,CAACqG,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzGhG,OAAA;UAAAqF,QAAA,gBAAKrF,OAAA;YAAM+E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC9F,MAAM,CAACqG,WAAW,CAACU,SAAS;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFhG,OAAA;UAAAqF,QAAA,gBAAKrF,OAAA;YAAM+E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC9F,MAAM,CAACqG,WAAW,CAACW,UAAU,CAACP,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGhG,OAAA;UAAAqF,QAAA,gBAAKrF,OAAA;YAAM+E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC9F,MAAM,CAACqG,WAAW,CAACY,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FhG,OAAA;UAAAqF,QAAA,gBAAKrF,OAAA;YAAM+E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC9F,MAAM,CAACqG,WAAW,CAACa,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAElH,MAAM,CAACqG,WAAW,CAACa,UAAU;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnIhG,OAAA;UAAAqF,QAAA,gBAAKrF,OAAA;YAAM+E,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAAC9F,MAAM,CAACqG,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDhG,OAAA;MAAK+E,KAAK,EAAE;QACVsC,IAAI,EAAE,CAAC;QACPpC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBoC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE;MACZ,CAAE;MAAAlC,QAAA,EACCtE,QAAQ,KAAK,OAAO;MAAA;MACnB;MACAf,OAAA;QAAK+E,KAAK,EAAE;UACVsC,IAAI,EAAE,CAAC;UACPpC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,gBAEArF,OAAA;UAAK+E,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BgC,YAAY,EAAE,MAAM;YACpBvB,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,gBACArF,OAAA;YAAK+E,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEM,UAAU,EAAE,QAAQ;cAAEI,GAAG,EAAE;YAAM,CAAE;YAAAN,QAAA,eAChErF,OAAA;cAAAqF,QAAA,GAAM,0HAAyB,EAAC1E,aAAa,CAACgG,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAGNhG,OAAA;YAAK+E,KAAK,EAAE;cACVE,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBI,GAAG,EAAE,KAAK;cACV4B,QAAQ,EAAE,UAAU;cACpBE,MAAM,EAAE,IAAI,CAAC;YACf,CAAE;YAAApC,QAAA,gBACArF,OAAA;cACEmG,OAAO,EAAExB,OAAQ;cACjB+C,QAAQ,EAAEzG,SAAS,IAAI,GAAI;cAC3B8D,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAElE,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnDmE,KAAK,EAAEnE,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACzCmF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAEpF,SAAS,IAAI,GAAG,GAAG,aAAa,GAAG,SAAS;gBACpDgF,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEThG,OAAA;cAAM+E,KAAK,EAAE;gBACX6C,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,QAAQ;gBACnB5B,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAEnE,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG;cACpC,CAAE;cAAAoE,QAAA,GACC,CAACpE,SAAS,GAAG,GAAG,EAAE0F,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEPhG,OAAA;cACEmG,OAAO,EAAEzB,MAAO;cAChBgD,QAAQ,EAAEzG,SAAS,IAAI,CAAE;cACzB8D,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAElE,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACjDmE,KAAK,EAAEnE,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACvCmF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAEpF,SAAS,IAAI,CAAC,GAAG,aAAa,GAAG,SAAS;gBAClDgF,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEThG,OAAA;cACEmG,OAAO,EAAE1B,SAAU;cACnBiD,QAAQ,EAAEzG,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAE;cACpEyD,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAGlE,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBAC9F8D,KAAK,EAAGnE,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBACpF8E,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAGpF,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,aAAa,GAAG,SAAS;gBAC/F2E,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,0BAAM;cAAAtC,QAAA,EACb;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhG,OAAA;UACE8H,GAAG,EAAEnG,YAAa;UAClBoD,KAAK,EAAE;YACLwC,QAAQ,EAAE,UAAU;YACpB7D,KAAK,EAAE,MAAM;YACbsB,MAAM,EAAE,MAAM;YACdqB,MAAM,EAAExF,UAAU,GAAG,WAAW,GAAG,SAAS;YAC5CkH,UAAU,EAAE,MAAM;YAClB9C,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB8B,QAAQ,EAAE,MAAM,CAAC;UACnB,CAAE;UAAAjC,QAAA,EAEDlF,aAAa,iBACZH,OAAA;YAAK+E,KAAK,EAAE;cACVwC,QAAQ,EAAE,UAAU;cACpB7D,KAAK,EAAE,MAAM;cACbsB,MAAM,EAAE,MAAM;cACdgD,SAAS,EAAE,qBAAqB;cAChC/C,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,eAEArF,OAAA;cACE8H,GAAG,EAAEjG,iBAAkB;cACvBkD,KAAK,EAAE;gBACLwC,QAAQ,EAAE,UAAU;gBACpBtC,OAAO,EAAE,cAAc;gBACvBmB,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnB0B,QAAQ,EAAE,QAAQ;gBAClBW,SAAS,EAAE,SAAShH,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtGiH,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAE/E,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1D8E,MAAM,EAAEpF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACF4G,OAAO,EAAEvE,WAAY;cACrBwE,WAAW,EAAElE,oBAAqB;cAAAmB,QAAA,GAGjC9E,UAAU,gBACTP,OAAA;gBAAK+E,KAAK,EAAE;kBACVrB,KAAK,EAAE,OAAO;kBACdsB,MAAM,EAAE,OAAO;kBACfC,OAAO,EAAE,MAAM;kBACfM,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBJ,KAAK,EAAE,SAAS;kBAChBD,eAAe,EAAE;gBACnB,CAAE;gBAAAE,QAAA,EAAC;cAEH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAENhG,OAAA;gBACE8H,GAAG,EAAElG,gBAAiB;gBACtByG,GAAG,EAAE,gCAAgCnI,MAAM,CAACmC,YAAY,MAAMiG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBAC3EC,GAAG,EAAC,0BAAM;gBACVC,MAAM,EAAE3G,eAAgB;gBACxB4G,OAAO,EAAE3G,gBAAiB;gBAC1BgD,KAAK,EAAE;kBACL4D,QAAQ,EAAE,MAAM;kBAChBX,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdC,OAAO,EAAE,OAAO;kBAChB2D,OAAO,EAAEvI,WAAW,GAAG,CAAC,GAAG,GAAG;kBAC9BwI,cAAc,EAAE,MAAM,CAAC;gBACzB;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EAGA3F,WAAW,iBACVL,OAAA;gBACE+E,KAAK,EAAE;kBACLwC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,CAAC;kBACNzF,IAAI,EAAE,CAAC;kBACPK,KAAK,EAAE,GAAG/C,aAAa,GAAG;kBAC1BqE,MAAM,EAAE,MAAM;kBACdsC,QAAQ,EAAE;gBACZ,CAAE;gBAAAjC,QAAA,eAEFrF,OAAA;kBACEqI,GAAG,EAAElI,aAAc;kBACnBqI,GAAG,EAAC,0BAAM;kBACVzD,KAAK,EAAE;oBACL;oBACArB,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG/C,aAAa,GAAG;oBACtCqE,MAAM,EAAE,MAAM;oBACd+D,SAAS,EAAE,MAAM;oBAAE;oBACnBF,cAAc,EAAE,WAAW;oBAAE;oBAC7BtB,QAAQ,EAAE,UAAU;oBACpBlE,IAAI,EAAE,CAAC;oBACPyF,GAAG,EAAE;kBACP;gBAAE;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EAGA3F,WAAW,iBACVL,OAAA;gBACE,mBAAgB,MAAM;gBACtB+E,KAAK,EAAE;kBACLwC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,CAAC;kBACNzF,IAAI,EAAE,GAAG1C,aAAa,GAAG;kBACzB+C,KAAK,EAAE,KAAK;kBACZsB,MAAM,EAAE,MAAM;kBACdG,eAAe,EAAE,SAAS;kBAC1BkB,MAAM,EAAE,WAAW;kBACnB2C,SAAS,EAAE,iCAAiC;kBAC5Cf,SAAS,EAAE,kBAAkB;kBAC7BR,MAAM,EAAE;gBACV,CAAE;gBACFW,WAAW,EAAExF,oBAAqB;gBAAAyC,QAAA,eAGlCrF,OAAA;kBACE+E,KAAK,EAAE;oBACLwC,QAAQ,EAAE,UAAU;oBACpBuB,GAAG,EAAE,KAAK;oBACVzF,IAAI,EAAE,KAAK;oBACX4E,SAAS,EAAE,uBAAuB;oBAClCvE,KAAK,EAAE,MAAM;oBACbsB,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,SAAS;oBAC1BS,YAAY,EAAE,KAAK;oBACnBX,OAAO,EAAE,MAAM;oBACfM,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBJ,KAAK,EAAE,OAAO;oBACda,QAAQ,EAAE,MAAM;oBAChBgD,UAAU,EAAE,MAAM;oBAClBD,SAAS,EAAE;kBACb,CAAE;kBAAA3D,QAAA,EACH;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA,CAAC3F,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;gBAAK+E,KAAK,EAAE;kBACVwC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,KAAK;kBACVzF,IAAI,EAAE,KAAK;kBACX4E,SAAS,EAAE,uBAAuB;kBAClC7C,KAAK,EAAE,MAAM;kBACbD,eAAe,EAAE,oBAAoB;kBACrCM,OAAO,EAAE,UAAU;kBACnBG,YAAY,EAAE,KAAK;kBACnBK,QAAQ,EAAE;gBACZ,CAAE;gBAAAZ,QAAA,EAAC;cAEH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;MAAA;MAEN;MACAhG,OAAA;QAAK+E,KAAK,EAAE;UACVsC,IAAI,EAAE,CAAC;UACPpC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,gBAEArF,OAAA;UAAK+E,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BgC,YAAY,EAAE,MAAM;YACpBvB,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,gBACArF,OAAA;YAAAqF,QAAA,EAAM;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGnBhG,OAAA;YAAK+E,KAAK,EAAE;cACVE,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBI,GAAG,EAAE,KAAK;cACV4B,QAAQ,EAAE,UAAU;cACpBE,MAAM,EAAE,IAAI,CAAC;YACf,CAAE;YAAApC,QAAA,gBACArF,OAAA;cACEmG,OAAO,EAAExB,OAAQ;cACjB+C,QAAQ,EAAEzG,SAAS,IAAI,GAAI;cAC3B8D,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAElE,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnDmE,KAAK,EAAEnE,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACzCmF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAEpF,SAAS,IAAI,GAAG,GAAG,aAAa,GAAG,SAAS;gBACpDgF,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEThG,OAAA;cAAM+E,KAAK,EAAE;gBACX6C,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,QAAQ;gBACnB5B,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAEnE,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG;cACpC,CAAE;cAAAoE,QAAA,GACC,CAACpE,SAAS,GAAG,GAAG,EAAE0F,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEPhG,OAAA;cACEmG,OAAO,EAAEzB,MAAO;cAChBgD,QAAQ,EAAEzG,SAAS,IAAI,CAAE;cACzB8D,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAElE,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACjDmE,KAAK,EAAEnE,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACvCmF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAEpF,SAAS,IAAI,CAAC,GAAG,aAAa,GAAG,SAAS;gBAClDgF,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEThG,OAAA;cACEmG,OAAO,EAAE1B,SAAU;cACnBiD,QAAQ,EAAEzG,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAE;cACpEyD,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAGlE,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBAC9F8D,KAAK,EAAGnE,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBACpF8E,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAGpF,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,aAAa,GAAG,SAAS;gBAC/F2E,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,0BAAM;cAAAtC,QAAA,EACb;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhG,OAAA;UAAK+E,KAAK,EAAE;YACVsC,IAAI,EAAE,CAAC;YACPpC,OAAO,EAAE,MAAM;YACfU,GAAG,EAAE,MAAM;YACXJ,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB8B,QAAQ,EAAE,MAAM,CAAC;UACnB,CAAE;UAAAjC,QAAA,GAEDlF,aAAa,iBACZH,OAAA;YAAK+E,KAAK,EAAE;cACVsC,IAAI,EAAE,GAAG;cACTQ,SAAS,EAAE,QAAQ;cACnBc,QAAQ,EAAE;YACZ,CAAE;YAAAtD,QAAA,gBACArF,OAAA;cAAK+E,KAAK,EAAE;gBACVyC,YAAY,EAAE,KAAK;gBACnBvB,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhG,OAAA;cACE+E,KAAK,EAAE;gBACLE,OAAO,EAAE,cAAc;gBACvBgD,SAAS,EAAE,SAAShH,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtGiH,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAE/E,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1D8E,MAAM,EAAEpF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACF4G,OAAO,EAAEvE,WAAY;cACrBwE,WAAW,EAAElE,oBAAqB;cAAAmB,QAAA,eAElCrF,OAAA;gBACEqI,GAAG,EAAElI,aAAc;gBACnBqI,GAAG,EAAC,0BAAM;gBACVzD,KAAK,EAAE;kBACL4D,QAAQ,EAAE,MAAM;kBAChBX,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdoB,MAAM,EAAE,mBAAmB;kBAC3BR,YAAY,EAAE,KAAK;kBACnBmD,SAAS,EAAE,SAAS;kBACpBF,cAAc,EAAE,WAAW,CAAC;gBAC9B;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDhG,OAAA;YAAK+E,KAAK,EAAE;cACVsC,IAAI,EAAE,GAAG;cACTQ,SAAS,EAAE,QAAQ;cACnBc,QAAQ,EAAE;YACZ,CAAE;YAAAtD,QAAA,gBACArF,OAAA;cAAK+E,KAAK,EAAE;gBACVyC,YAAY,EAAE,KAAK;gBACnBvB,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAEL,CAAC3F,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;cAAK+E,KAAK,EAAE;gBACVrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,OAAO;gBACfC,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBY,MAAM,EAAE,iBAAiB;gBACzBR,YAAY,EAAE,KAAK;gBACnBR,KAAK,EAAE,MAAM;gBACbD,eAAe,EAAE;cACnB,CAAE;cAAAE,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,EAEAzF,UAAU,iBACTP,OAAA;cAAK+E,KAAK,EAAE;gBACVrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,OAAO;gBACfC,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBY,MAAM,EAAE,mBAAmB;gBAC3BR,YAAY,EAAE,KAAK;gBACnBR,KAAK,EAAE,SAAS;gBAChBD,eAAe,EAAE;cACnB,CAAE;cAAAE,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,eAEDhG,OAAA;cACE+E,KAAK,EAAE;gBACLE,OAAO,EAAE5E,WAAW,GAAG,cAAc,GAAG,MAAM;gBAC9C4H,SAAS,EAAE,SAAShH,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtGiH,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAE/E,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1D8E,MAAM,EAAEpF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACF4G,OAAO,EAAEvE,WAAY;cACrBwE,WAAW,EAAElE,oBAAqB;cAAAmB,QAAA,eAElCrF,OAAA;gBACEqI,GAAG,EAAE,gCAAgCnI,MAAM,CAACmC,YAAY,MAAMiG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBAC3EC,GAAG,EAAC,0BAAM;gBACVC,MAAM,EAAE3G,eAAgB;gBACxB4G,OAAO,EAAE3G,gBAAiB;gBAC1BgD,KAAK,EAAE;kBACL4D,QAAQ,EAAE,MAAM;kBAChBX,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdoB,MAAM,EAAE,mBAAmB;kBAC3BR,YAAY,EAAE,KAAK;kBACnBmD,SAAS,EAAE;gBACb;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL3F,WAAW,iBACVL,OAAA;MAAK+E,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,MAAM;QACvB+D,SAAS,EAAE,gBAAgB;QAC3BjE,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBG,GAAG,EAAE,MAAM;QACXF,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBACArF,OAAA;QACEmG,OAAO,EAAEnE,aAAc;QACvB+C,KAAK,EAAE;UACLU,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdgB,MAAM,EAAE,MAAM;UACdR,YAAY,EAAE,KAAK;UACnBS,MAAM,EAAE,SAAS;UACjBJ,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGhE,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,SAAU;QAChE2B,YAAY,EAAGjE,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhErF,OAAA;UAAAqF,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,4BAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAEThG,OAAA;QACEoC,IAAI,EAAE,gCAAgClC,MAAM,CAACmC,YAAY,EAAG;QAC5D8B,MAAM,EAAC,QAAQ;QACfgF,GAAG,EAAC,qBAAqB;QACzBpE,KAAK,EAAE;UACLU,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdgE,cAAc,EAAE,MAAM;UACtBxD,YAAY,EAAE,KAAK;UACnBK,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGhE,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,SAAU;QAChE2B,YAAY,EAAGjE,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhErF,OAAA;UAAAqF,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,kCAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJhG,OAAA;QAAK+E,KAAK,EAAE;UACVkB,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE,MAAM;UACbiE,UAAU,EAAE;QACd,CAAE;QAAAhE,QAAA,GAAC,gBACG,EAACnF,MAAM,CAACqC,QAAQ;MAAA;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5F,EAAA,CAn4BIH,UAAU;AAAAqJ,EAAA,GAAVrJ,UAAU;AAq4BhB,eAAeA,UAAU;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}