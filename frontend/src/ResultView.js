import React, { useState, useRef, useEffect } from 'react';

const ResultView = ({ result, originalImage }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [showParams, setShowParams] = useState(false);
  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比
  const [isDragging, setIsDragging] = useState(false);
  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'
  const [zoomLevel, setZoomLevel] = useState(1); // 缩放级别
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 }); // 平移偏移
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });
  const containerRef = useRef(null);
  const enhancedImageRef = useRef(null);
  const imageContainerRef = useRef(null);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  const downloadImage = () => {
    const link = document.createElement('a');
    link.href = `http://localhost:8001/result/${result.enhanced_url}`;
    link.download = `enhanced_${result.filename}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 分割线拖拽处理
  const handleSplitMouseDown = (e) => {
    setIsDragging(true);
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡，避免触发图像平移
  };

  const handleSplitMouseMove = (e) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSplitPosition(percentage);
  };

  const handleSplitMouseUp = () => {
    setIsDragging(false);
  };

  // 缩放处理
  const handleWheel = (e) => {
    if (!imageContainerRef.current) return;

    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));
    setZoomLevel(newZoom);
  };

  // 平移处理
  const handlePanStart = (e) => {
    if (zoomLevel <= 1 || isDragging) return; // 只有放大时才允许平移，且不在拖拽分割线时

    setIsPanning(true);
    setLastPanPoint({
      x: e.clientX,
      y: e.clientY
    });
    e.preventDefault();
  };

  // 图像容器的鼠标按下处理（避免与分割线拖拽冲突）
  const handleImageMouseDown = (e) => {
    // 检查是否点击在分割线上（通过检查目标元素）
    if (e.target.closest('[data-split-line]')) {
      return; // 如果点击在分割线上，不处理平移
    }

    if (zoomLevel > 1 && !isDragging) {
      handlePanStart(e);
    }
  };

  const handlePanMove = (e) => {
    if (!isPanning) return;

    const deltaX = e.clientX - lastPanPoint.x;
    const deltaY = e.clientY - lastPanPoint.y;

    setPanOffset(prev => ({
      x: prev.x + deltaX,
      y: prev.y + deltaY
    }));

    setLastPanPoint({
      x: e.clientX,
      y: e.clientY
    });
  };

  const handlePanEnd = () => {
    setIsPanning(false);
  };

  // 重置缩放和平移
  const resetZoom = () => {
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
  };

  // 缩放控制函数
  const zoomIn = () => {
    setZoomLevel(prev => Math.min(5, prev + 0.25));
  };

  const zoomOut = () => {
    setZoomLevel(prev => Math.max(0.5, prev - 0.25));
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleSplitMouseMove);
      document.addEventListener('mouseup', handleSplitMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleSplitMouseMove);
        document.removeEventListener('mouseup', handleSplitMouseUp);
      };
    }
  }, [isDragging]);

  // 添加平移事件监听
  useEffect(() => {
    if (isPanning) {
      document.addEventListener('mousemove', handlePanMove);
      document.addEventListener('mouseup', handlePanEnd);
      return () => {
        document.removeEventListener('mousemove', handlePanMove);
        document.removeEventListener('mouseup', handlePanEnd);
      };
    }
  }, [isPanning, lastPanPoint]);



  // 重置分割线位置
  const resetSplit = () => {
    setSplitPosition(50);
  };

  return (
    <div style={{ 
      width: '100%', 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#1e1e1e',
      color: '#ddd'
    }}>
      {/* 顶部工具栏 */}
      <div style={{
        height: '40px',
        backgroundColor: '#2b2b2b',
        borderBottom: '1px solid #555',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 16px',
        flexShrink: 0
      }}>
        {/* 左侧状态指示 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: '#28ca42'
          }}></div>
          <span style={{ fontSize: '13px', color: '#ddd' }}>处理完成</span>
          {result.message && (
            <span style={{ fontSize: '12px', color: '#aaa' }}>• {result.message}</span>
          )}
        </div>

        {/* 右侧视图控制 */}
        <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>
          <button
            onClick={() => setViewMode('split')}
            style={{
              padding: '4px 8px',
              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer',
              fontSize: '12px',
              transition: 'background-color 0.2s'
            }}
          >
            分割对比
          </button>
          <button
            onClick={() => setViewMode('side-by-side')}
            style={{
              padding: '4px 8px',
              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer',
              fontSize: '12px',
              transition: 'background-color 0.2s'
            }}
          >
            并排对比
          </button>
          {viewMode === 'split' && (
            <button
              onClick={resetSplit}
              style={{
                padding: '4px 8px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '12px',
                transition: 'background-color 0.2s'
              }}
            >
              重置
            </button>
          )}
        </div>
      </div>

      {/* 参数信息栏 */}
      {result.params_used && (
        <div style={{
          height: showParams ? 'auto' : '32px',
          backgroundColor: '#2b2b2b',
          borderBottom: '1px solid #555',
          padding: '8px 16px',
          flexShrink: 0,
          transition: 'height 0.3s ease'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <span style={{ fontSize: '12px', color: '#aaa' }}>处理参数:</span>
              {!showParams && (
                <div style={{ display: 'flex', gap: '12px', fontSize: '12px', color: '#ddd' }}>
                  <span>{result.params_used.scale}x超分</span>
                  <span>{result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'}</span>
                  <span>锐化{(result.params_used.sharpening * 100).toFixed(0)}%</span>
                  <span>美颜{(result.params_used.beauty * 100).toFixed(0)}%</span>
                </div>
              )}
            </div>
            <button
              onClick={() => setShowParams(!showParams)}
              style={{
                padding: '2px 6px',
                backgroundColor: 'transparent',
                color: '#aaa',
                border: '1px solid #555',
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '11px',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#555';
                e.target.style.color = '#fff';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
                e.target.style.color = '#aaa';
              }}
            >
              {showParams ? '▲' : '▼'}
            </button>
          </div>

          {showParams && (
            <div style={{ 
              marginTop: '12px', 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
              gap: '8px',
              fontSize: '12px',
              color: '#ddd'
            }}>
              <div><span style={{ color: '#aaa' }}>超分倍数:</span> {result.params_used.scale}x</div>
              <div><span style={{ color: '#aaa' }}>AI模型:</span> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>
              <div><span style={{ color: '#aaa' }}>锐化:</span> {(result.params_used.sharpening * 100).toFixed(0)}%</div>
              <div><span style={{ color: '#aaa' }}>降噪:</span> {result.params_used.denoising}</div>
              <div><span style={{ color: '#aaa' }}>饱和度:</span> {result.params_used.saturation.toFixed(1)}</div>
              <div><span style={{ color: '#aaa' }}>对比度:</span> {result.params_used.contrast.toFixed(1)}</div>
              <div><span style={{ color: '#aaa' }}>亮度:</span> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>
              <div><span style={{ color: '#aaa' }}>美颜:</span> {(result.params_used.beauty * 100).toFixed(0)}%</div>
            </div>
          )}
        </div>
      )}

      {/* 主图像显示区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column',
        overflow: 'hidden',
        position: 'relative'
      }}>
        {viewMode === 'split' ? (
          // 分割线对比模式
          <div style={{ 
            flex: 1, 
            display: 'flex', 
            flexDirection: 'column',
            padding: '16px'
          }}>
            {/* 分割线信息栏和缩放控制 */}
            <div style={{
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '12px',
              fontSize: '12px',
              color: '#aaa'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>左侧：原始图像 | 右侧：增强图像 | 分割位置：{splitPosition.toFixed(0)}%</span>
              </div>

              {/* 缩放控制按钮 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                position: 'relative',
                zIndex: 1000 // 确保按钮始终在最上层
              }}>
                <button
                  onClick={zoomOut}
                  disabled={zoomLevel <= 0.5}
                  style={{
                    width: '24px',
                    height: '24px',
                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',
                    color: zoomLevel <= 0.5 ? '#666' : '#fff',
                    border: '1px solid #666',
                    borderRadius: '3px',
                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    zIndex: 1001
                  }}
                  title="缩小"
                >
                  −
                </button>

                <span style={{
                  minWidth: '40px',
                  textAlign: 'center',
                  fontSize: '11px',
                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'
                }}>
                  {(zoomLevel * 100).toFixed(0)}%
                </span>

                <button
                  onClick={zoomIn}
                  disabled={zoomLevel >= 5}
                  style={{
                    width: '24px',
                    height: '24px',
                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',
                    color: zoomLevel >= 5 ? '#666' : '#fff',
                    border: '1px solid #666',
                    borderRadius: '3px',
                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    zIndex: 1001
                  }}
                  title="放大"
                >
                  +
                </button>

                <button
                  onClick={resetZoom}
                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}
                  style={{
                    width: '24px',
                    height: '24px',
                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',
                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',
                    border: '1px solid #666',
                    borderRadius: '3px',
                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',
                    fontSize: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    zIndex: 1001
                  }}
                  title="重置缩放"
                >
                  ⌂
                </button>
              </div>
            </div>

            <div
              ref={containerRef}
              style={{
                position: 'relative',
                width: '100%',
                height: '100%',
                cursor: isDragging ? 'ew-resize' : 'default',
                userSelect: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'auto' // 添加滚动条支持
              }}
            >
              {originalImage && (
                <div style={{ 
                  position: 'relative', 
                  width: '100%', 
                  height: '100%',
                  maxHeight: 'calc(100vh - 200px)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {/* 图像对比容器 - 支持缩放和平移 */}
                  <div
                    ref={imageContainerRef}
                    style={{
                      position: 'relative',
                      display: 'inline-block',
                      border: '1px solid #555',
                      borderRadius: '4px',
                      overflow: 'hidden',
                      transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,
                      transformOrigin: 'center center',
                      transition: isPanning ? 'none' : 'transform 0.2s ease-out',
                      cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'
                    }}
                    onWheel={handleWheel}
                    onMouseDown={handleImageMouseDown}
                  >
                    {/* 增强图像作为背景 */}
                    {imageError ? (
                      <div style={{
                        width: '400px',
                        height: '300px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#ff6b6b',
                        backgroundColor: '#2b2b2b'
                      }}>
                        增强图像加载失败
                      </div>
                    ) : (
                      <img
                        ref={enhancedImageRef}
                        src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}
                        alt="增强图像"
                        onLoad={handleImageLoad}
                        onError={handleImageError}
                        style={{
                          maxWidth: '100%',
                          maxHeight: 'calc(100vh - 250px)',
                          height: 'auto',
                          display: 'block',
                          opacity: imageLoaded ? 1 : 0.5,
                          imageRendering: 'auto' // 增强图像使用高质量渲染
                        }}
                      />
                    )}

                    {/* 原始图像覆盖层 - 像素级对齐 */}
                    {imageLoaded && (
                      <div
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: `${splitPosition}%`,
                          height: '100%',
                          overflow: 'hidden'
                        }}
                      >
                        <img
                          src={originalImage}
                          alt="原始图像"
                          style={{
                            // 确保原图与增强图片尺寸完全匹配
                            width: `${100 * 100 / splitPosition}%`,
                            height: '100%',
                            objectFit: 'fill', // 强制填充，实现最近邻插值效果
                            imageRendering: 'pixelated', // 最近邻插值，保持像素清晰
                            position: 'absolute',
                            left: 0,
                            top: 0
                          }}
                        />
                      </div>
                    )}

                    {/* 分割线 */}
                    {imageLoaded && (
                      <div
                        data-split-line="true"
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: `${splitPosition}%`,
                          width: '2px',
                          height: '100%',
                          backgroundColor: '#4a90e2',
                          cursor: 'ew-resize',
                          boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',
                          transform: 'translateX(-1px)',
                          zIndex: 10
                        }}
                        onMouseDown={handleSplitMouseDown}
                      >
                        {/* 分割线手柄 */}
                        <div
                          style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            width: '16px',
                            height: '32px',
                            backgroundColor: '#4a90e2',
                            borderRadius: '8px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: '10px',
                            fontWeight: 'bold',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.3)'
                          }}
                        >
                          ⟷
                        </div>
                      </div>
                    )}

                    {/* 加载状态 */}
                    {!imageLoaded && !imageError && (
                      <div style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        color: '#aaa',
                        backgroundColor: 'rgba(43,43,43,0.9)',
                        padding: '8px 12px',
                        borderRadius: '4px',
                        fontSize: '12px'
                      }}>
                        加载中...
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          // 并排对比模式
          <div style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            padding: '16px'
          }}>
            {/* 并排对比控制栏 */}
            <div style={{
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '12px',
              fontSize: '12px',
              color: '#aaa'
            }}>
              <span>并排对比模式</span>

              {/* 缩放控制按钮 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                position: 'relative',
                zIndex: 1000 // 确保按钮始终在最上层
              }}>
                <button
                  onClick={zoomOut}
                  disabled={zoomLevel <= 0.5}
                  style={{
                    width: '24px',
                    height: '24px',
                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',
                    color: zoomLevel <= 0.5 ? '#666' : '#fff',
                    border: '1px solid #666',
                    borderRadius: '3px',
                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    zIndex: 1001
                  }}
                  title="缩小"
                >
                  −
                </button>

                <span style={{
                  minWidth: '40px',
                  textAlign: 'center',
                  fontSize: '11px',
                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'
                }}>
                  {(zoomLevel * 100).toFixed(0)}%
                </span>

                <button
                  onClick={zoomIn}
                  disabled={zoomLevel >= 5}
                  style={{
                    width: '24px',
                    height: '24px',
                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',
                    color: zoomLevel >= 5 ? '#666' : '#fff',
                    border: '1px solid #666',
                    borderRadius: '3px',
                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    zIndex: 1001
                  }}
                  title="放大"
                >
                  +
                </button>

                <button
                  onClick={resetZoom}
                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}
                  style={{
                    width: '24px',
                    height: '24px',
                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',
                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',
                    border: '1px solid #666',
                    borderRadius: '3px',
                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',
                    fontSize: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    zIndex: 1001
                  }}
                  title="重置缩放"
                >
                  ⌂
                </button>
              </div>
            </div>

            {/* 并排图像容器 */}
            <div style={{
              flex: 1,
              display: 'flex',
              gap: '16px',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'auto' // 添加滚动条支持
            }}>
            {/* 原始图像 */}
            {originalImage && (
              <div style={{ 
                flex: '1', 
                textAlign: 'center',
                maxWidth: '50%'
              }}>
                <div style={{ 
                  marginBottom: '8px', 
                  fontSize: '12px', 
                  color: '#aaa' 
                }}>
                  原始图像
                </div>
                <div
                  style={{
                    display: 'inline-block',
                    transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,
                    transformOrigin: 'center center',
                    transition: isPanning ? 'none' : 'transform 0.2s ease-out',
                    cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'
                  }}
                  onWheel={handleWheel}
                  onMouseDown={handleImageMouseDown}
                >
                  <img
                    src={originalImage}
                    alt="原始图像"
                    style={{
                      maxWidth: '100%',
                      maxHeight: 'calc(100vh - 300px)',
                      height: 'auto',
                      border: '1px solid #4a90e2',
                      borderRadius: '4px',
                      objectFit: 'contain'
                    }}
                  />
                </div>
              </div>
            )}

            {/* 增强图像 */}
            <div style={{ 
              flex: '1', 
              textAlign: 'center',
              maxWidth: '50%'
            }}>
              <div style={{ 
                marginBottom: '8px', 
                fontSize: '12px', 
                color: '#aaa' 
              }}>
                增强图像
              </div>

              {!imageLoaded && !imageError && (
                <div style={{
                  width: '100%',
                  height: '200px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px dashed #666',
                  borderRadius: '4px',
                  color: '#aaa',
                  backgroundColor: '#2b2b2b'
                }}>
                  正在加载图像...
                </div>
              )}

              {imageError && (
                <div style={{
                  width: '100%',
                  height: '200px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px solid #ff6b6b',
                  borderRadius: '4px',
                  color: '#ff6b6b',
                  backgroundColor: '#2b2b2b'
                }}>
                  图像加载失败
                </div>
              )}

              <div
                style={{
                  display: imageLoaded ? 'inline-block' : 'none',
                  transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,
                  transformOrigin: 'center center',
                  transition: isPanning ? 'none' : 'transform 0.2s ease-out',
                  cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'
                }}
                onWheel={handleWheel}
                onMouseDown={handleImageMouseDown}
              >
                <img
                  src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}
                  alt="增强图像"
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                  style={{
                    maxWidth: '100%',
                    maxHeight: 'calc(100vh - 300px)',
                    height: 'auto',
                    border: '1px solid #28ca42',
                    borderRadius: '4px',
                    objectFit: 'contain'
                  }}
                />
              </div>
            </div>
            </div>
          </div>
        )}
      </div>

      {/* 底部控制栏 */}
      {imageLoaded && (
        <div style={{
          height: '48px',
          backgroundColor: '#333',
          borderTop: '1px solid #555',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '12px',
          padding: '0 16px',
          flexShrink: 0
        }}>
          <button
            onClick={downloadImage}
            style={{
              padding: '6px 12px',
              backgroundColor: '#28ca42',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'background-color 0.2s'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#22a83a'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#28ca42'}
          >
            <span>📥</span>
            下载图像
          </button>

          <a
            href={`http://localhost:8001/result/${result.enhanced_url}`}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              padding: '6px 12px',
              backgroundColor: '#4a90e2',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px',
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'background-color 0.2s'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#357abd'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#4a90e2'}
          >
            <span>🔍</span>
            新窗口查看
          </a>

          <div style={{
            fontSize: '12px',
            color: '#aaa',
            marginLeft: 'auto'
          }}>
            文件: {result.filename}
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultView;
